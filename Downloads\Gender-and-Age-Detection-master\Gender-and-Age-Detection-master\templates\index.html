<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gender & Age Detection</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header>
            <h1><i class="fas fa-user-friends"></i> Gender & Age Detection</h1>
            <p>Upload an image or use your webcam to detect gender and age using AI</p>
        </header>

        <div class="main-content">
            <!-- Tab Navigation -->
            <div class="tab-container">
                <button class="tab-button active" onclick="openTab(event, 'upload-tab')">
                    <i class="fas fa-upload"></i> Upload Image
                </button>
                <button class="tab-button" onclick="openTab(event, 'webcam-tab')">
                    <i class="fas fa-camera"></i> Use Webcam
                </button>
            </div>

            <!-- Upload Tab -->
            <div id="upload-tab" class="tab-content active">
                <div class="upload-section">
                    <div class="upload-area" id="uploadArea">
                        <div class="upload-content">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <h3>Drag & Drop your image here</h3>
                            <p>or click to browse</p>
                            <input type="file" id="fileInput" accept="image/*" hidden>
                        </div>
                    </div>
                    <button class="btn btn-primary" onclick="document.getElementById('fileInput').click()">
                        <i class="fas fa-folder-open"></i> Choose File
                    </button>
                </div>
            </div>

            <!-- Webcam Tab -->
            <div id="webcam-tab" class="tab-content">
                <div class="webcam-section">
                    <video id="webcam" autoplay muted></video>
                    <canvas id="canvas" hidden></canvas>
                    <div class="webcam-controls">
                        <button class="btn btn-primary" id="startWebcam">
                            <i class="fas fa-video"></i> Start Camera
                        </button>
                        <button class="btn btn-secondary" id="captureBtn" disabled>
                            <i class="fas fa-camera"></i> Capture Photo
                        </button>
                        <button class="btn btn-danger" id="stopWebcam" disabled>
                            <i class="fas fa-stop"></i> Stop Camera
                        </button>
                    </div>
                </div>
            </div>

            <!-- Loading Indicator -->
            <div class="loading" id="loading" hidden>
                <div class="spinner"></div>
                <p>Analyzing image...</p>
            </div>

            <!-- Results Section -->
            <div class="results-section" id="resultsSection" hidden>
                <h2><i class="fas fa-chart-bar"></i> Detection Results</h2>
                <div class="result-image-container">
                    <img id="resultImage" alt="Processed image">
                </div>
                <div class="results-grid" id="resultsGrid">
                    <!-- Results will be populated here -->
                </div>
            </div>

            <!-- Error Section -->
            <div class="error-section" id="errorSection" hidden>
                <div class="error-message">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span id="errorText"></span>
                </div>
            </div>
        </div>

        <footer>
            <p>Powered by OpenCV and Deep Learning | Built with ❤️</p>
        </footer>
    </div>

    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
</body>
</html>

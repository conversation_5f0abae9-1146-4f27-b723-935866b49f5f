import cv2
import numpy as np
import base64
from io import BytesIO
from PIL import Image

class GenderAgeDetectionService:
    def __init__(self):
        # Model paths
        self.face_proto = "opencv_face_detector.pbtxt"
        self.face_model = "opencv_face_detector_uint8.pb"
        self.age_proto = "age_deploy.prototxt"
        self.age_model = "age_net.caffemodel"
        self.gender_proto = "gender_deploy.prototxt"
        self.gender_model = "gender_net.caffemodel"
        
        # Model parameters
        self.MODEL_MEAN_VALUES = (78.4263377603, 87.7689143744, 114.895847746)
        self.age_list = ['(0-2)', '(4-6)', '(8-12)', '(15-20)', '(25-32)', '(38-43)', '(48-53)', '(60-100)']
        self.gender_list = ['Male', 'Female']
        
        # Load models
        self.face_net = cv2.dnn.readNet(self.face_model, self.face_proto)
        self.age_net = cv2.dnn.readNet(self.age_model, self.age_proto)
        self.gender_net = cv2.dnn.readNet(self.gender_model, self.gender_proto)
        
    def highlight_face(self, frame, conf_threshold=0.7):
        """Detect faces in the frame and return face boxes"""
        frame_opencv_dnn = frame.copy()
        frame_height = frame_opencv_dnn.shape[0]
        frame_width = frame_opencv_dnn.shape[1]
        blob = cv2.dnn.blobFromImage(frame_opencv_dnn, 1.0, (300, 300), [104, 117, 123], True, False)

        self.face_net.setInput(blob)
        detections = self.face_net.forward()
        face_boxes = []
        
        for i in range(detections.shape[2]):
            confidence = detections[0, 0, i, 2]
            if confidence > conf_threshold:
                x1 = int(detections[0, 0, i, 3] * frame_width)
                y1 = int(detections[0, 0, i, 4] * frame_height)
                x2 = int(detections[0, 0, i, 5] * frame_width)
                y2 = int(detections[0, 0, i, 6] * frame_height)
                face_boxes.append([x1, y1, x2, y2])
                cv2.rectangle(frame_opencv_dnn, (x1, y1), (x2, y2), (0, 255, 0), int(round(frame_height/150)), 8)
        
        return frame_opencv_dnn, face_boxes
    
    def predict_age_gender(self, face):
        """Predict age and gender for a face region"""
        blob = cv2.dnn.blobFromImage(face, 1.0, (227, 227), self.MODEL_MEAN_VALUES, swapRB=False)
        
        # Gender prediction
        self.gender_net.setInput(blob)
        gender_preds = self.gender_net.forward()
        gender = self.gender_list[gender_preds[0].argmax()]
        gender_confidence = gender_preds[0].max()
        
        # Age prediction
        self.age_net.setInput(blob)
        age_preds = self.age_net.forward()
        age = self.age_list[age_preds[0].argmax()]
        age_confidence = age_preds[0].max()
        
        return gender, age, gender_confidence, age_confidence
    
    def process_image(self, image_data, is_base64=False):
        """Process an image and return results with detected faces"""
        try:
            if is_base64:
                # Decode base64 image
                image_data = base64.b64decode(image_data.split(',')[1])
                image = Image.open(BytesIO(image_data))
                frame = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
            else:
                # Read image file
                frame = cv2.imread(image_data)
            
            if frame is None:
                return {"error": "Could not read image"}
            
            # Detect faces
            result_img, face_boxes = self.highlight_face(frame)
            
            if not face_boxes:
                return {"error": "No face detected", "image": self._encode_image(result_img)}
            
            results = []
            padding = 20
            
            for face_box in face_boxes:
                # Extract face region
                face = frame[max(0, face_box[1] - padding):
                           min(face_box[3] + padding, frame.shape[0] - 1),
                           max(0, face_box[0] - padding):
                           min(face_box[2] + padding, frame.shape[1] - 1)]
                
                # Predict age and gender
                gender, age, gender_conf, age_conf = self.predict_age_gender(face)
                
                # Add text to result image
                label = f'{gender}, {age}'
                cv2.putText(result_img, label, (face_box[0], face_box[1] - 10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 255), 2, cv2.LINE_AA)
                
                results.append({
                    "gender": gender,
                    "age": age.strip('()'),
                    "gender_confidence": float(gender_conf),
                    "age_confidence": float(age_conf),
                    "face_box": face_box
                })
            
            return {
                "success": True,
                "faces_detected": len(face_boxes),
                "results": results,
                "image": self._encode_image(result_img)
            }
            
        except Exception as e:
            return {"error": f"Processing failed: {str(e)}"}
    
    def _encode_image(self, image):
        """Encode image to base64 for web display"""
        _, buffer = cv2.imencode('.jpg', image)
        img_base64 = base64.b64encode(buffer).decode('utf-8')
        return f"data:image/jpeg;base64,{img_base64}"
